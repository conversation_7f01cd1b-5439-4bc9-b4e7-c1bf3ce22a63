'use client';

import React, { useState, useRef, useCallback } from 'react';
import ReactCrop, { Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';

interface ImageData {
  id?: number;
  url: string;
  file?: File;
  isNew?: boolean;
}

interface MultipleImageUploadProps {
  images: ImageData[];
  onImagesChange: (images: ImageData[]) => void;
  onError?: (error: string) => void;
  maxImages?: number;
  className?: string;
  onDeleteImage?: (imageId: number) => Promise<void>;
}

interface CropModalProps {
  src: string;
  onCropComplete: (croppedImageBlob: Blob) => void;
  onCancel: () => void;
}

function CropModal({ src, onCropComplete, onCancel }: CropModalProps) {
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    width: 90,
    height: 90,
    x: 5,
    y: 5,
  });
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const imgRef = useRef<HTMLImageElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const onImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    const size = Math.min(width, height);
    const x = (width - size) / 2;
    const y = (height - size) / 2;
    
    setCrop({
      unit: 'px',
      width: size,
      height: size,
      x,
      y,
    });
  }, []);

  const getCroppedImg = useCallback(async () => {
    if (!completedCrop || !imgRef.current || !canvasRef.current) {
      return;
    }

    const image = imgRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      return;
    }

    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;

    canvas.width = completedCrop.width;
    canvas.height = completedCrop.height;

    ctx.drawImage(
      image,
      completedCrop.x * scaleX,
      completedCrop.y * scaleY,
      completedCrop.width * scaleX,
      completedCrop.height * scaleY,
      0,
      0,
      completedCrop.width,
      completedCrop.height
    );

    return new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        }
      }, 'image/jpeg', 0.9);
    });
  }, [completedCrop]);

  const handleCropComplete = async () => {
    const croppedBlob = await getCroppedImg();
    if (croppedBlob) {
      onCropComplete(croppedBlob);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl max-h-[90vh] overflow-auto">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Crop Image (1:1 Aspect Ratio)
        </h3>
        
        <div className="mb-4">
          <ReactCrop
            crop={crop}
            onChange={(_, percentCrop) => setCrop(percentCrop)}
            onComplete={(c) => setCompletedCrop(c)}
            aspect={1}
            minWidth={100}
            minHeight={100}
          >
            <img
              ref={imgRef}
              alt="Crop preview"
              src={src}
              style={{ maxHeight: '60vh', maxWidth: '100%' }}
              onLoad={onImageLoad}
            />
          </ReactCrop>
        </div>

        <canvas
          ref={canvasRef}
          style={{ display: 'none' }}
        />

        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleCropComplete}
            className="px-4 py-2 bg-[#E6B120] text-white rounded-md text-sm font-medium hover:bg-[#FFCD29]"
          >
            Crop & Add
          </button>
        </div>
      </div>
    </div>
  );
}

export default function MultipleImageUpload({
  images,
  onImagesChange,
  onError,
  maxImages = 5,
  className = '',
  onDeleteImage
}: MultipleImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [cropModalSrc, setCropModalSrc] = useState<string | null>(null);
  const [pendingFile, setPendingFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    if (images.length >= maxImages) {
      onError?.(`Maximum ${maxImages} images allowed.`);
      return;
    }

    const file = files[0];
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      onError?.('Please select an image file.');
      return;
    }

    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
      onError?.('File size must be less than 10MB.');
      return;
    }

    // Create preview URL for cropping
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setCropModalSrc(e.target.result as string);
        setPendingFile(file);
      }
    };
    reader.readAsDataURL(file);
  };

  const handleCropComplete = async (croppedBlob: Blob) => {
    if (!pendingFile) return;

    setIsUploading(true);
    setCropModalSrc(null);

    try {
      // Create a new file from the cropped blob
      const croppedFile = new File([croppedBlob], pendingFile.name, {
        type: 'image/jpeg',
        lastModified: Date.now(),
      });

      // Create a temporary URL for preview
      const tempUrl = URL.createObjectURL(croppedBlob);
      
      // Add to images array
      const newImage: ImageData = {
        url: tempUrl,
        file: croppedFile,
        isNew: true,
      };

      onImagesChange([...images, newImage]);
    } catch (error) {
      onError?.('Failed to process image');
    } finally {
      setIsUploading(false);
      setPendingFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleCropCancel = () => {
    setCropModalSrc(null);
    setPendingFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRemoveImage = async (index: number) => {
    const newImages = [...images];
    const removedImage = newImages[index];

    try {
      // If it's an existing image (has ID), delete from database
      if (removedImage.id && onDeleteImage) {
        await onDeleteImage(removedImage.id);
      }

      // Revoke object URL if it's a temporary one
      if (removedImage.isNew && removedImage.url.startsWith('blob:')) {
        URL.revokeObjectURL(removedImage.url);
      }

      newImages.splice(index, 1);
      onImagesChange(newImages);
    } catch (error) {
      onError?.('Failed to delete image');
    }
  };

  return (
    <>
      <div className={`space-y-4 ${className}`}>
        {/* Image Gallery */}
        {images.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {images.map((image, index) => (
              <div key={index} className="relative group">
                <img
                  src={image.url}
                  alt={`Product image ${index + 1}`}
                  className="w-full h-24 object-cover rounded-lg border border-gray-200"
                />
                <button
                  onClick={() => handleRemoveImage(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  ×
                </button>
                {image.isNew && (
                  <div className="absolute bottom-1 left-1 bg-green-500 text-white text-xs px-1 rounded">
                    New
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Upload Button */}
        {images.length < maxImages && (
          <div className="relative">
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
              disabled={isUploading}
            />
            
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
              className={`w-full px-4 py-3 border-2 border-dashed border-gray-300 rounded-lg text-center hover:border-[#E6B120] focus:outline-none focus:ring-2 focus:ring-[#E6B120] focus:border-transparent transition-colors ${
                isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
              }`}
            >
              {isUploading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#E6B120] mr-2"></div>
                  Processing...
                </div>
              ) : (
                <div className="flex flex-col items-center py-2">
                  <svg className="w-8 h-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span className="text-sm text-gray-600">
                    Add Image ({images.length}/{maxImages})
                  </span>
                  <span className="text-xs text-gray-400 mt-1">
                    Will be cropped to 1:1 ratio and converted to WebP
                  </span>
                </div>
              )}
            </button>
          </div>
        )}
      </div>

      {cropModalSrc && (
        <CropModal
          src={cropModalSrc}
          onCropComplete={handleCropComplete}
          onCancel={handleCropCancel}
        />
      )}
    </>
  );
}
